{"version": 3, "file": "useStateAndRef.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useStateAndRef.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,iFAAiF;AACjF,6EAA6E;AAC7E,8BAA8B;AAC9B,MAAM,CAAC,MAAM,cAAc,GAAG,CAI5B,YAAe,EACf,EAAE;IACF,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAI,YAAY,CAAC,CAAC;IACtD,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAI,YAAY,CAAC,CAAC;IAE1C,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CACxC,CAAC,kBAAkB,EAAE,EAAE;QACrB,IAAI,QAAW,CAAC;QAChB,IAAI,OAAO,kBAAkB,KAAK,UAAU,EAAE,CAAC;YAC7C,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,kBAAkB,CAAC;QAChC,CAAC;QACD,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnB,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC;IACzB,CAAC,EACD,EAAE,CACH,CAAC;IAEF,OAAO,CAAC,GAAG,EAAE,gBAAgB,CAAU,CAAC;AAC1C,CAAC,CAAC"}