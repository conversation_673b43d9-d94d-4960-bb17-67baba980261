import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const ConsoleSummaryDisplay = ({ errorCount, }) => {
    if (errorCount === 0) {
        return null;
    }
    return (_jsx(Box, { children: _jsxs(Text, { color: Colors.AccentRed, children: [errorCount, " error", errorCount > 1 ? 's' : '', ' ', _jsx(Text, { color: Colors.Gray, children: "(ctrl+o)" })] }) }));
};
//# sourceMappingURL=ConsoleSummaryDisplay.js.map