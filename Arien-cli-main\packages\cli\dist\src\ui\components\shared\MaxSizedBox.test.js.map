{"version": 3, "file": "MaxSizedBox.test.js", "sourceRoot": "", "sources": ["../../../../../src/ui/components/shared/MaxSizedBox.test.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AACrE,OAAO,EAAE,WAAW,EAAE,uBAAuB,EAAE,MAAM,kBAAkB,CAAC;AACxE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAE9C,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,+DAA+D;IAC/D,0DAA0D;IAC1D,uEAAuE;IACvE,wDAAwD;IACxD,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAE9B,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;QAC3D,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,YACtC,KAAC,GAAG,cACF,KAAC,IAAI,gCAAqB,GACtB,GACM,GACG,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;QACpD,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,MAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,aACrC,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,IACM,GACG,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;OACxB,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uFAAuF,EAAE,GAAG,EAAE;QAC/F,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,MAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,iBAAiB,EAAC,QAAQ,aACjE,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,IACM,GACG,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;4BACH,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAC1C,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,YACrC,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,4CAAmC,GAChD,GACM,GACG,CACpB,CAAC;QAEF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;;QAEvB,CAAC,CAAC;IACR,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;QAC1D,MAAM,aAAa,GAAG;;4BAEE,CAAC;QACzB,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,MAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,aACtC,KAAC,GAAG,cACF,KAAC,IAAI,0BAAe,GAChB,EACN,MAAC,GAAG,eACF,KAAC,IAAI,4BAAiB,EACtB,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,YAAE,aAAa,GAAQ,IACpC,EACN,MAAC,GAAG,eACF,KAAC,IAAI,mCAAwB,EAC7B,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,4CAAmC,IAChD,IACM,GACG,CACpB,CAAC;QAEF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CACxB;;;;;;;;;;;;;;oBAcc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;QAC9D,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,WAAW,IAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACpC,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,mDAA0C,GACvD,GACM,GACG,CACpB,CAAC;QAEF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;;;;KAI1B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;QACvD,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,MAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,aAC7C,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,IACM,GACG,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;OACxB,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;QAChE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,MAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,aACrC,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,IACM,GACG,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;OACxB,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wFAAwF,EAAE,GAAG,EAAE;QAChG,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,MAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,iBAAiB,EAAC,QAAQ,aACjE,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,IACM,GACG,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;4BACH,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACjD,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,GAAgB,GACvC,CACpB,CAAC;QACF,sDAAsD;QACtD,+CAA+C;QAC/C,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;QACjE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,WAAW,IAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACpC,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,yCAAY,GACzB,GACM,GACG,CACpB,CAAC;QAEF,8DAA8D;QAC9D,8DAA8D;QAC9D,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;GAC5B,CAAC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;QAC/D,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,WAAW,IAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACpC,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,6EAAkB,GAC/B,GACM,GACG,CACpB,CAAC;QAEF,qCAAqC;QACrC,kDAAkD;QAClD,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;;GAE5B,CAAC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACjD,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,MAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,0BAA0B,EAAE,CAAC,aACpE,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,EACN,KAAC,GAAG,cACF,KAAC,IAAI,yBAAc,GACf,IACM,GACG,CACpB,CAAC;QACF,2DAA2D;QAC3D,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;OACxB,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,MAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,aACtC,8BACE,KAAC,GAAG,cACF,KAAC,IAAI,uCAA4B,GAC7B,EACN,KAAC,GAAG,cACF,KAAC,IAAI,uCAA4B,GAC7B,IACL,EACH,KAAC,GAAG,cACF,KAAC,IAAI,sCAA2B,GAC5B,IACM,GACG,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;;oBAEX,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;QACrD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAC7B,EAAE,MAAM,EAAE,EAAE,EAAE,EACd,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAC1B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,YACtC,KAAC,GAAG,cACF,KAAC,IAAI,cAAE,YAAY,GAAQ,GACvB,GACM,GACG,CACpB,CAAC;QAEF,MAAM,QAAQ,GAAG;YACf,+BAA+B;YAC/B,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC;SACzD,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;QACxD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAC7B,EAAE,MAAM,EAAE,EAAE,EAAE,EACd,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAC1B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,WAAW,IAAC,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,iBAAiB,EAAC,QAAQ,YAClE,KAAC,GAAG,cACF,KAAC,IAAI,cAAE,YAAY,GAAQ,GACvB,GACM,GACG,CACpB,CAAC;QAEF,MAAM,QAAQ,GAAG;YACf,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACvD,8BAA8B;SAC/B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}