import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { shortenPath, tildeifyPath, tokenLimit } from '@arien/arien-cli-core';
import { ConsoleSummaryDisplay } from './ConsoleSummaryDisplay.js';
import process from 'node:process';
import { MemoryUsageDisplay } from './MemoryUsageDisplay.js';
export const Footer = ({ model, targetDir, branchName, debugMode, debugMessage, corgiMode, errorCount, showErrorDetails, showMemoryUsage, totalTokenCount, }) => {
    const limit = tokenLimit(model);
    const percentage = totalTokenCount / limit;
    const contextLeft = ((1 - percentage) * 100).toFixed(0);
    // Enhanced visual indicator for context usage with better thresholds
    const getContextColor = () => {
        if (percentage > 0.9)
            return Colors.AccentRed;
        if (percentage > 0.75)
            return Colors.AccentYellow;
        if (percentage > 0.5)
            return Colors.AccentCyan;
        return Colors.AccentGreen;
    };
    const getContextIndicator = () => {
        if (percentage > 0.9)
            return ' ⚠ Critical';
        if (percentage > 0.75)
            return ' ⚡ High';
        if (percentage > 0.5)
            return ' ◉ Medium';
        return ' ◆ Good';
    };
    // Enhanced sandbox status with better visual indicators
    const getSandboxStatus = () => {
        if (process.env.SANDBOX && process.env.SANDBOX !== 'sandbox-exec') {
            return {
                text: process.env.SANDBOX.replace(/^arien-(?:cli-)?/, ''),
                color: Colors.AccentGreen,
                icon: '◆',
            };
        }
        else if (process.env.SANDBOX === 'sandbox-exec') {
            return {
                text: `MacOS Seatbelt (${process.env.SEATBELT_PROFILE})`,
                color: Colors.AccentYellow,
                icon: '◇',
            };
        }
        else {
            return {
                text: 'no sandbox (see /docs)',
                color: Colors.AccentRed,
                icon: '◈',
            };
        }
    };
    const sandboxStatus = getSandboxStatus();
    return (_jsxs(Box, { flexDirection: "column", width: "100%", marginTop: 1, children: [_jsx(Box, { marginBottom: 1, width: "100%", justifyContent: "center", children: _jsx(Text, { color: Colors.Gray, children: '─'.repeat(80) }) }), _jsxs(Box, { justifyContent: "space-between", width: "100%", alignItems: "flex-start", children: [_jsxs(Box, { flexDirection: "column", minWidth: "30%", children: [_jsxs(Box, { alignItems: "center", children: [_jsx(Text, { color: Colors.AccentBlue, bold: true, children: "\u25C6 " }), _jsx(Text, { color: Colors.LightBlue, children: shortenPath(tildeifyPath(targetDir), 50) }), branchName && (_jsxs(Box, { marginLeft: 1, children: [_jsx(Text, { color: Colors.AccentCyan, children: "\u22C4" }), _jsxs(Text, { color: Colors.Gray, children: [" ", branchName] })] }))] }), debugMode && (_jsxs(Box, { marginTop: 0, alignItems: "center", children: [_jsx(Text, { color: Colors.AccentRed, bold: true, children: "\u26A1 " }), _jsx(Text, { color: Colors.AccentRed, dimColor: true, children: debugMessage || 'debug mode active' })] }))] }), _jsxs(Box, { flexGrow: 1, alignItems: "center", justifyContent: "center", minWidth: "30%", children: [_jsxs(Text, { color: sandboxStatus.color, bold: true, children: [sandboxStatus.icon, ' '] }), _jsx(Text, { color: sandboxStatus.color, children: sandboxStatus.text })] }), _jsxs(Box, { alignItems: "flex-end", flexDirection: "column", minWidth: "30%", children: [_jsxs(Box, { alignItems: "center", justifyContent: "flex-end", children: [_jsx(Text, { color: Colors.AccentBlue, bold: true, children: model }), _jsxs(Text, { color: getContextColor(), bold: true, children: [' ', "(", contextLeft, "% left", getContextIndicator(), ")"] })] }), _jsxs(Box, { alignItems: "center", justifyContent: "flex-end", marginTop: 0, children: [corgiMode && (_jsxs(Box, { marginRight: 1, children: [_jsx(Text, { color: Colors.AccentRed, children: "\u25BC" }), _jsx(Text, { color: Colors.Foreground, children: "(\u00B4" }), _jsx(Text, { color: Colors.AccentRed, children: "\u1D25" }), _jsx(Text, { color: Colors.Foreground, children: "`)" }), _jsx(Text, { color: Colors.AccentRed, children: "\u25BC" })] })), !showErrorDetails && errorCount > 0 && (_jsx(Box, { marginRight: 1, children: _jsx(ConsoleSummaryDisplay, { errorCount: errorCount }) })), showMemoryUsage && (_jsx(Box, { children: _jsx(MemoryUsageDisplay, {}) }))] })] })] })] }));
};
//# sourceMappingURL=Footer.js.map