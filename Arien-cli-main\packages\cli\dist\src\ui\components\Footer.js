import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { shortenPath, tildeifyPath, tokenLimit } from '@arien/arien-cli-core';
import process from 'node:process';
import { MemoryUsageDisplay } from './MemoryUsageDisplay.js';
export const Footer = ({ model, targetDir, branchName, debugMode, debugMessage, corgiMode, errorCount, showErrorDetails, showMemoryUsage, promptTokenCount, candidatesTokenCount, totalTokenCount, }) => {
    const tokenLimitForModel = tokenLimit(model);
    const contextLeft = Math.round(((tokenLimitForModel - totalTokenCount) / tokenLimitForModel) * 100);
    const getContextColor = () => {
        if (contextLeft > 50)
            return Colors.AccentGreen;
        if (contextLeft > 20)
            return Colors.AccentYellow;
        return Colors.AccentRed;
    };
    const getContextIndicator = () => {
        if (contextLeft < 10)
            return ' - LOW';
        if (contextLeft < 30)
            return ' - MEDIUM';
        return '';
    };
    // Get directory display - shorter format
    const displayPath = tildeifyPath(shortenPath(targetDir, 30));
    const pathWithBranch = branchName ? `${displayPath}@${branchName}` : displayPath;
    // Sandbox status - more compact
    const getSandboxStatus = () => {
        if (process.env.SANDBOX && process.env.SANDBOX !== 'sandbox-exec') {
            return process.env.SANDBOX.replace(/^arien-(?:cli-)?/, '');
        }
        else if (process.env.SANDBOX === 'sandbox-exec') {
            return 'sandbox';
        }
        else {
            return 'no sandbox';
        }
    };
    const sandboxStatus = getSandboxStatus();
    const isNoSandbox = sandboxStatus === 'no sandbox';
    return (_jsxs(Box, { borderStyle: "single", borderColor: Colors.Gray, paddingX: 1, flexDirection: "row", justifyContent: "space-between", alignItems: "center", children: [_jsx(Box, { alignItems: "center", children: _jsx(Text, { color: Colors.AccentCyan, children: pathWithBranch }) }), _jsxs(Box, { alignItems: "center", children: [_jsx(Text, { color: isNoSandbox ? Colors.AccentRed : Colors.AccentGreen, children: sandboxStatus }), isNoSandbox && (_jsx(Text, { color: Colors.Gray, children: " (see /docs)" }))] }), _jsxs(Box, { alignItems: "center", children: [_jsx(Text, { color: Colors.AccentBlue, children: model }), _jsxs(Text, { color: getContextColor(), children: [' ', "(", contextLeft, "% left", getContextIndicator(), ")"] }), !showErrorDetails && errorCount > 0 && (_jsxs(Box, { marginLeft: 1, children: [_jsx(Text, { color: Colors.Gray, children: "|" }), _jsxs(Text, { color: Colors.AccentRed, children: [" ", errorCount, " err"] })] })), corgiMode && (_jsx(Text, { color: Colors.AccentRed, children: " [CORGI]" })), showMemoryUsage && _jsx(MemoryUsageDisplay, {})] })] }));
};
//# sourceMappingURL=Footer.js.map