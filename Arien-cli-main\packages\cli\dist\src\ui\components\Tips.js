import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const Tips = ({ config, showTips = true }) => {
    const arienMdFileCount = config.getArienMdFileCount();
    if (!showTips) {
        return null;
    }
    return (_jsxs(Box, { flexDirection: "row", marginBottom: 1, paddingLeft: 1, children: [_jsx(Text, { color: Colors.Gray, children: "Tip: " }), _jsxs(Text, { color: Colors.Foreground, children: ["Ask questions, edit files, run commands \u2022 Type ", _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "/help" }), " for shortcuts"] })] }));
};
//# sourceMappingURL=Tips.js.map