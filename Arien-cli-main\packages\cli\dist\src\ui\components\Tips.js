import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const Tips = ({ config }) => {
    const arienMdFileCount = config.getArienMdFileCount();
    const tips = [
        {
            icon: '◆',
            text: 'Ask questions, edit files, or run commands.',
            color: Colors.AccentBlue,
        },
        {
            icon: '◇',
            text: 'Be specific for the best results.',
            color: Colors.AccentCyan,
        },
        ...(arienMdFileCount === 0
            ? [
                {
                    icon: '◈',
                    text: (_jsxs(_Fragment, { children: ["Create", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "ARIEN.md" }), ' ', "files to customize your interactions with Arien AI."] })),
                    color: Colors.AccentGreen,
                },
            ]
            : []),
        {
            icon: '◉',
            text: (_jsxs(_Fragment, { children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "/help" }), ' ', "for more information."] })),
            color: Colors.AccentYellow,
        },
    ];
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, borderStyle: "round", borderColor: Colors.Gray, paddingX: 2, paddingY: 1, children: [_jsx(Box, { marginBottom: 1, alignItems: "center", children: _jsx(Text, { color: Colors.AccentPurple, bold: true, children: "\u25C6 Tips for getting started:" }) }), tips.map((tip, index) => (_jsxs(Box, { alignItems: "flex-start", marginBottom: 0, children: [_jsxs(Text, { color: tip.color, bold: true, children: [tip.icon, ' '] }), _jsx(Text, { color: Colors.Foreground, children: typeof tip.text === 'string' ? tip.text : tip.text })] }, index))), _jsx(Box, { marginTop: 1, justifyContent: "center", children: _jsx(Text, { color: Colors.Gray, dimColor: true, children: "Happy coding!" }) })] }));
};
//# sourceMappingURL=Tips.js.map