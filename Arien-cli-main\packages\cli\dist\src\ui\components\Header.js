import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import Gradient from 'ink-gradient';
import { Colors } from '../colors.js';
import { shortAsciiLogo, longAsciiLogo } from './AsciiArt.js';
import { getAsciiArtWidth } from '../utils/textUtils.js';
export const Header = ({ customAsciiArt, terminalWidth, }) => {
    let displayTitle;
    const widthOfLongLogo = getAsciiArtWidth(longAsciiLogo);
    const widthOfShortLogo = getAsciiArtWidth(shortAsciiLogo);
    if (customAsciiArt) {
        displayTitle = customAsciiArt;
    }
    else {
        // Enhanced responsive logo selection with more graceful fallbacks
        if (terminalWidth >= widthOfLongLogo + 20) {
            displayTitle = longAsciiLogo;
        }
        else if (terminalWidth >= widthOfShortLogo + 10) {
            displayTitle = shortAsciiLogo;
        }
        else if (terminalWidth >= 30) {
            // Compact fallback for medium terminals
            displayTitle = 'ARIEN AI';
        }
        else {
            // Minimal fallback for very narrow terminals
            displayTitle = 'Arien';
        }
    }
    const artWidth = getAsciiArtWidth(displayTitle);
    return (_jsxs(Box, { marginBottom: 1, alignItems: "center", justifyContent: "center", width: "100%", flexShrink: 0, paddingBottom: 1, children: [_jsx(Box, { paddingX: 1, paddingY: 0, alignItems: "center", justifyContent: "center", children: Colors.GradientColors ? (_jsx(Gradient, { colors: Colors.GradientColors, children: _jsx(Text, { children: displayTitle }) })) : (_jsx(Text, { color: Colors.AccentPurple, bold: true, children: displayTitle })) }), _jsx(Box, { marginTop: 1, width: "100%", justifyContent: "center", children: _jsx(Text, { color: Colors.Gray, children: '─'.repeat(Math.min(terminalWidth - 4, Math.max(artWidth, 20))) }) })] }));
};
//# sourceMappingURL=Header.js.map