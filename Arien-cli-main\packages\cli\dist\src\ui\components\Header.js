import { jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import Gradient from 'ink-gradient';
import { Colors } from '../colors.js';
import { shortAsciiLogo, longAsciiLogo } from './AsciiArt.js';
import { getAsciiArtWidth } from '../utils/textUtils.js';
export const Header = ({ customAsciiArt, terminalWidth, showMinimal = false, }) => {
    // Show minimal version after first interaction to save space
    if (showMinimal) {
        return (_jsx(Box, { marginBottom: 0, alignItems: "center", children: _jsx(Text, { color: Colors.AccentPurple, bold: true, children: "Arien AI" }) }));
    }
    let displayTitle;
    const widthOfLongLogo = getAsciiArtWidth(longAsciiLogo);
    const widthOfShortLogo = getAsciiArtWidth(shortAsciiLogo);
    if (customAsciiArt) {
        displayTitle = customAsciiArt;
    }
    else {
        // More aggressive responsive sizing to save space
        if (terminalWidth >= widthOfLongLogo + 20) {
            displayTitle = longAsciiLogo;
        }
        else if (terminalWidth >= widthOfShortLogo + 10) {
            displayTitle = shortAsciiLogo;
        }
        else {
            // Use text-based logo for small terminals
            displayTitle = 'ARIEN AI';
        }
    }
    const artWidth = getAsciiArtWidth(displayTitle);
    return (_jsx(Box, { marginBottom: 0, alignItems: "flex-start", width: artWidth, flexShrink: 0, children: Colors.GradientColors ? (_jsx(Gradient, { colors: Colors.GradientColors, children: _jsx(Text, { children: displayTitle }) })) : (_jsx(Text, { color: Colors.AccentPurple, children: displayTitle })) }));
};
//# sourceMappingURL=Header.js.map