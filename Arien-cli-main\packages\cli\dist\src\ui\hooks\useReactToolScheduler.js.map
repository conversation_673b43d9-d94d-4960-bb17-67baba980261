{"version": 3, "file": "useReactToolScheduler.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useReactToolScheduler.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EASL,iBAAiB,GAQlB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACvD,OAAO,EAGL,cAAc,GAEf,MAAM,aAAa,CAAC;AAmCrB,MAAM,UAAU,qBAAqB,CACnC,UAAgD,EAChD,MAAc,EACd,qBAEC,EACD,kBAAgD;IAEhD,MAAM,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,GAAG,QAAQ,CAE5D,EAAE,CAAC,CAAC;IAEN,MAAM,mBAAmB,GAAwB,WAAW,CAC1D,CAAC,UAAU,EAAE,WAAW,EAAE,EAAE;QAC1B,qBAAqB,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjC,IAAI,QAAQ,EAAE,IAAI,KAAK,YAAY,EAAE,CAAC;gBACpC,OAAO;oBACL,GAAG,QAAQ;oBACX,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CACxC,WAAW,CAAC,MAAM,KAAK,UAAU;wBACjC,WAAW,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS;wBAC7C,CAAC,CAAC,EAAE,GAAG,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE;wBAChD,CAAC,CAAC,WAAW,CAChB;iBACF,CAAC;YACJ,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,sBAAsB,CAAC,CAAC,SAAS,EAAE,EAAE,CACnC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;YACnB,IAAI,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,UAAU,IAAI,EAAE,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAClE,MAAM,WAAW,GAAG,EAA8B,CAAC;gBACnD,OAAO,EAAE,GAAG,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC;YACrD,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,EACD,CAAC,qBAAqB,CAAC,CACxB,CAAC;IAEF,MAAM,2BAA2B,GAAgC,WAAW,CAC1E,CAAC,kBAAkB,EAAE,EAAE;QACrB,UAAU,CAAC,kBAAkB,CAAC,CAAC;IACjC,CAAC,EACD,CAAC,UAAU,CAAC,CACb,CAAC;IAEF,MAAM,sBAAsB,GAA2B,WAAW,CAChE,CAAC,oBAAgC,EAAE,EAAE;QACnC,sBAAsB,CAAC,CAAC,gBAAgB,EAAE,EAAE,CAC1C,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAClC,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,IAAI,CAC/C,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,CAAC,MAAM,CACtD,CAAC;YACF,MAAM,cAAc,GAAoB;gBACtC,GAAG,MAAM;gBACT,yBAAyB,EACvB,mBAAmB,EAAE,yBAAyB,IAAI,KAAK;aACvC,CAAC;YACrB,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,EACD,CAAC,sBAAsB,CAAC,CACzB,CAAC;IAEF,MAAM,SAAS,GAAG,OAAO,CACvB,GAAG,EAAE,CACH,IAAI,iBAAiB,CAAC;QACpB,YAAY,EAAE,MAAM,CAAC,eAAe,EAAE;QACtC,mBAAmB;QACnB,sBAAsB,EAAE,2BAA2B;QACnD,iBAAiB,EAAE,sBAAsB;QACzC,YAAY,EAAE,MAAM,CAAC,eAAe,EAAE;QACtC,kBAAkB;QAClB,MAAM;KACP,CAAC,EACJ;QACE,MAAM;QACN,mBAAmB;QACnB,2BAA2B;QAC3B,sBAAsB;QACtB,kBAAkB;KACnB,CACF,CAAC;IAEF,MAAM,QAAQ,GAAe,WAAW,CACtC,CACE,OAAoD,EACpD,MAAmB,EACnB,EAAE;QACF,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACtC,CAAC,EACD,CAAC,SAAS,CAAC,CACZ,CAAC;IAEF,MAAM,oBAAoB,GAA2B,WAAW,CAC9D,CAAC,aAAuB,EAAE,EAAE;QAC1B,sBAAsB,CAAC,CAAC,SAAS,EAAE,EAAE,CACnC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CACnB,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;YACvC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE;YAC5C,CAAC,CAAC,EAAE,CACP,CACF,CAAC;IACJ,CAAC,EACD,EAAE,CACH,CAAC;IAEF,OAAO,CAAC,mBAAmB,EAAE,QAAQ,EAAE,oBAAoB,CAAC,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,SAAS,4BAA4B,CAAC,UAAsB;IAC1D,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,YAAY;YACf,OAAO,cAAc,CAAC,SAAS,CAAC;QAClC,KAAK,mBAAmB;YACtB,OAAO,cAAc,CAAC,UAAU,CAAC;QACnC,KAAK,WAAW;YACd,OAAO,cAAc,CAAC,SAAS,CAAC;QAClC,KAAK,SAAS;YACZ,OAAO,cAAc,CAAC,OAAO,CAAC;QAChC,KAAK,WAAW;YACd,OAAO,cAAc,CAAC,QAAQ,CAAC;QACjC,KAAK,OAAO;YACV,OAAO,cAAc,CAAC,KAAK,CAAC;QAC9B,KAAK,WAAW;YACd,OAAO,cAAc,CAAC,OAAO,CAAC;QAChC,OAAO,CAAC,CAAC,CAAC;YACR,MAAM,eAAe,GAAU,UAAU,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,oCAAoC,eAAe,EAAE,CAAC,CAAC;YACpE,OAAO,cAAc,CAAC,KAAK,CAAC;QAC9B,CAAC;IACH,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAC1B,WAAgD;IAEhD,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;IAE3E,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAChC,CAAC,WAAW,EAA6B,EAAE;QACzC,IAAI,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;QAC3C,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,sBAAsB,GAAG,KAAK,CAAC;QAEnC,MAAM,mBAAmB,GACvB,MAAM,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI;YACvC,CAAC,CAAE,WAA8B,CAAC,IAAI;YACtC,CAAC,CAAC,SAAS,CAAC;QAEhB,IAAI,mBAAmB,EAAE,CAAC;YACxB,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC;YAC9C,WAAW,GAAG,mBAAmB,CAAC,cAAc,CAC9C,WAAW,CAAC,OAAO,CAAC,IAAI,CACzB,CAAC;YACF,sBAAsB,GAAG,mBAAmB,CAAC,gBAAgB,CAAC;QAChE,CAAC;aAAM,IAAI,SAAS,IAAI,WAAW,IAAI,MAAM,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACrE,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,qBAAqB,GAGvB;YACF,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM;YAClC,IAAI,EAAE,WAAW;YACjB,WAAW;YACX,sBAAsB;SACvB,CAAC;QAEF,QAAQ,WAAW,CAAC,MAAM,EAAE,CAAC;YAC3B,KAAK,SAAS;gBACZ,OAAO;oBACL,GAAG,qBAAqB;oBACxB,MAAM,EAAE,4BAA4B,CAAC,WAAW,CAAC,MAAM,CAAC;oBACxD,aAAa,EAAE,WAAW,CAAC,QAAQ,CAAC,aAAa;oBACjD,mBAAmB,EAAE,SAAS;iBAC/B,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,GAAG,qBAAqB;oBACxB,IAAI,EAAE,mBAAmB,EAAE,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI;oBAClE,MAAM,EAAE,4BAA4B,CAAC,WAAW,CAAC,MAAM,CAAC;oBACxD,aAAa,EAAE,WAAW,CAAC,QAAQ,CAAC,aAAa;oBACjD,mBAAmB,EAAE,SAAS;iBAC/B,CAAC;YACJ,KAAK,WAAW;gBACd,OAAO;oBACL,GAAG,qBAAqB;oBACxB,MAAM,EAAE,4BAA4B,CAAC,WAAW,CAAC,MAAM,CAAC;oBACxD,aAAa,EAAE,WAAW,CAAC,QAAQ,CAAC,aAAa;oBACjD,mBAAmB,EAAE,SAAS;iBAC/B,CAAC;YACJ,KAAK,mBAAmB;gBACtB,OAAO;oBACL,GAAG,qBAAqB;oBACxB,MAAM,EAAE,4BAA4B,CAAC,WAAW,CAAC,MAAM,CAAC;oBACxD,aAAa,EAAE,SAAS;oBACxB,mBAAmB,EAAE,WAAW,CAAC,mBAAmB;iBACrD,CAAC;YACJ,KAAK,WAAW;gBACd,OAAO;oBACL,GAAG,qBAAqB;oBACxB,MAAM,EAAE,4BAA4B,CAAC,WAAW,CAAC,MAAM,CAAC;oBACxD,aAAa,EACV,WAAwC,CAAC,UAAU,IAAI,SAAS;oBACnE,mBAAmB,EAAE,SAAS;iBAC/B,CAAC;YACJ,KAAK,YAAY,CAAC,CAAC,cAAc;YACjC,KAAK,WAAW;gBACd,OAAO;oBACL,GAAG,qBAAqB;oBACxB,MAAM,EAAE,4BAA4B,CAAC,WAAW,CAAC,MAAM,CAAC;oBACxD,aAAa,EAAE,SAAS;oBACxB,mBAAmB,EAAE,SAAS;iBAC/B,CAAC;YACJ,OAAO,CAAC,CAAC,CAAC;gBACR,MAAM,eAAe,GAAU,WAAW,CAAC;gBAC3C,OAAO;oBACL,MAAM,EAAG,eAAmC,CAAC,OAAO,CAAC,MAAM;oBAC3D,IAAI,EAAE,cAAc;oBACpB,WAAW,EAAE,yCAAyC;oBACtD,MAAM,EAAE,cAAc,CAAC,KAAK;oBAC5B,aAAa,EAAE,yBAAyB;oBACxC,mBAAmB,EAAE,SAAS;oBAC9B,sBAAsB,EAAE,KAAK;iBAC9B,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC,CACF,CAAC;IAEF,OAAO;QACL,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,YAAY;KACpB,CAAC;AACJ,CAAC"}