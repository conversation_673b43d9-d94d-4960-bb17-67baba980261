import { jsx as _jsx } from "react/jsx-runtime";
import { Text } from 'ink';
import { useStreamingContext } from '../contexts/StreamingContext.js';
import { StreamingState } from '../types.js';
import { AnimatedIcon } from './shared/AnimatedIcon.js';
import { Colors } from '../colors.js';
export const ArienRespondingSpinner = ({ nonRespondingDisplay }) => {
    const streamingState = useStreamingContext();
    if (streamingState === StreamingState.Responding) {
        return _jsx(AnimatedIcon, { isPending: true, color: Colors.AccentPurple });
    }
    else if (nonRespondingDisplay) {
        return _jsx(Text, { children: nonRespondingDisplay });
    }
    return null;
};
//# sourceMappingURL=ArienRespondingSpinner.js.map