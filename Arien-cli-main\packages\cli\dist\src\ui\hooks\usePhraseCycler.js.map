{"version": 3, "file": "usePhraseCycler.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/usePhraseCycler.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAEpD,MAAM,CAAC,MAAM,qBAAqB,GAAG;IACnC,mBAAmB;IACnB,0BAA0B;IAC1B,gCAAgC;IAChC,8BAA8B;IAC9B,mCAAmC;IACnC,yBAAyB;IACzB,+BAA+B;IAC/B,iCAAiC;IACjC,4BAA4B;IAC5B,6BAA6B;IAC7B,uCAAuC;IACvC,wBAAwB;IACxB,uBAAuB;IACvB,kCAAkC;IAClC,+CAA+C;IAC/C,iCAAiC;IACjC,yBAAyB;IACzB,2BAA2B;IAC3B,yBAAyB;IACzB,oBAAoB;IACpB,kCAAkC;IAClC,+BAA+B;IAC/B,sCAAsC;IACtC,4BAA4B;IAC5B,kCAAkC;IAClC,gDAAgD;IAChD,4BAA4B;IAC5B,qDAAqD;IACrD,oDAAoD;IACpD,+BAA+B;IAC/B,8CAA8C;IAC9C,oCAAoC;IACpC,gCAAgC;IAChC,0CAA0C;IAC1C,wCAAwC;IACxC,6BAA6B;IAC7B,gCAAgC;IAChC,oDAAoD;IACpD,oCAAoC;IACpC,0BAA0B;IAC1B,sCAAsC;IACtC,qCAAqC;IACrC,4BAA4B;IAC5B,mCAAmC;IACnC,qCAAqC;IACrC,yBAAyB;IACzB,4CAA4C;IAC5C,kBAAkB;IAClB,gCAAgC;IAChC,mCAAmC;IACnC,4CAA4C;IAC5C,sCAAsC;IACtC,2CAA2C;IAC3C,6CAA6C;IAC7C,2CAA2C;IAC3C,yCAAyC;IACzC,6CAA6C;IAC7C,uBAAuB;IACvB,uCAAuC;IACvC,uCAAuC;IACvC,gBAAgB;IAChB,+BAA+B;IAC/B,sCAAsC;IACtC,6BAA6B;IAC7B,+CAA+C;IAC/C,8BAA8B;IAC9B,4BAA4B;IAC5B,qDAAqD;IACrD,mDAAmD;IACnD,gDAAgD;IAChD,8CAA8C;IAC9C,6BAA6B;IAC7B,yBAAyB;IACzB,yBAAyB;IACzB,0CAA0C;IAC1C,2CAA2C;IAC3C,6BAA6B;IAC7B,iDAAiD;IACjD,oCAAoC;IACpC,oBAAoB;IACpB,wBAAwB;IACxB,sDAAsD;IACtD,sBAAsB;IACtB,4BAA4B;IAC5B,gDAAgD;IAChD,iDAAiD;IACjD,uCAAuC;IACvC,oBAAoB;IACpB,uCAAuC;IACvC,6CAA6C;IAC7C,yBAAyB;IACzB,gDAAgD;IAChD,8DAA8D;IAC9D,4DAA4D;IAC5D,qEAAqE;IACrE,qEAAqE;IACrE,kEAAkE;IAClE,oCAAoC;IACpC,8CAA8C;IAC9C,oDAAoD;IACpD,+CAA+C;IAC/C,uBAAuB;IACvB,kCAAkC;IAClC,mDAAmD;IACnD,SAAS;IACT,iCAAiC;IACjC,iCAAiC;IACjC,sCAAsC;IACtC,kCAAkC;IAClC,wCAAwC;IACxC,sBAAsB;IACtB,yFAAyF;IACzF,+BAA+B;IAC/B,iDAAiD;IACjD,6DAA6D;IAC7D,gCAAgC;IAChC,mCAAmC;IACnC,uDAAuD;IACvD,yBAAyB;IACzB,8CAA8C;IAC9C,uDAAuD;IACvD,4CAA4C;IAC5C,2CAA2C;IAC3C,oDAAoD;IACpD,gDAAgD;IAChD,kCAAkC;IAClC,0CAA0C;IAC1C,gEAAgE;IAChE,0CAA0C;IAC1C,2DAA2D;IAC3D,2EAA2E;CAC5E,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAAG,KAAK,CAAC;AAE/C;;;;;GAKG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,QAAiB,EAAE,SAAkB,EAAE,EAAE;IACvE,MAAM,CAAC,oBAAoB,EAAE,uBAAuB,CAAC,GAAG,QAAQ,CAC9D,qBAAqB,CAAC,CAAC,CAAC,CACzB,CAAC;IACF,MAAM,iBAAiB,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAE9D,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,SAAS,EAAE,CAAC;YACd,uBAAuB,CAAC,kCAAkC,CAAC,CAAC;YAC5D,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC9B,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACzC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;YACnC,CAAC;QACH,CAAC;aAAM,IAAI,QAAQ,EAAE,CAAC;YACpB,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC9B,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAC3C,CAAC;YACD,kCAAkC;YAClC,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CACnC,IAAI,CAAC,MAAM,EAAE,GAAG,qBAAqB,CAAC,MAAM,CAC7C,CAAC;YACF,uBAAuB,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAEnE,iBAAiB,CAAC,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE;gBAC3C,6BAA6B;gBAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAC5B,IAAI,CAAC,MAAM,EAAE,GAAG,qBAAqB,CAAC,MAAM,CAC7C,CAAC;gBACF,uBAAuB,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC;YAC9D,CAAC,EAAE,yBAAyB,CAAC,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,kDAAkD;YAClD,uDAAuD;YACvD,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC9B,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACzC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;YACnC,CAAC;YACD,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,GAAG,EAAE;YACV,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC9B,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACzC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;YACnC,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;IAE1B,OAAO,oBAAoB,CAAC;AAC9B,CAAC,CAAC"}