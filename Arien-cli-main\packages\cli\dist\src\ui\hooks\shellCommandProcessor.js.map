{"version": 3, "file": "shellCommandProcessor.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/shellCommandProcessor.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAE/C,OAAO,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAGpC,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAEjD,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,SAAS,MAAM,YAAY,CAAC;AAEnC,MAAM,yBAAyB,GAAG,IAAI,CAAC;AACvC,MAAM,iBAAiB,GAAG,KAAK,CAAC;AAchC;;;;;;;;;;GAUG;AACH,SAAS,mBAAmB,CAC1B,gBAAwB,EACxB,GAAW,EACX,WAAwB,EACxB,aAAsC,EACtC,cAAyC;IAEzC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC;QAC5C,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7C,MAAM,SAAS,GAAG,SAAS;YACzB,CAAC,CAAC,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC1B,CAAC,CAAC,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAE7B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE;YACpC,GAAG;YACH,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;YACjC,QAAQ,EAAE,CAAC,SAAS,EAAE,uDAAuD;SAC9E,CAAC,CAAC;QAEH,8EAA8E;QAC9E,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;QAEhD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAI,KAAK,GAAiB,IAAI,CAAC;QAC/B,IAAI,MAAM,GAAG,KAAK,CAAC;QAEnB,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,MAAM,cAAc,GAAG,IAAI,CAAC;QAC5B,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,MAA2B,EAAE,EAAE;YACjE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAExB,IAAI,UAAU,IAAI,YAAY,GAAG,cAAc,EAAE,CAAC;gBAChD,uEAAuE;gBACvE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gBAC7D,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC;gBAElC,IAAI,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC1B,UAAU,GAAG,KAAK,CAAC;oBACnB,0EAA0E;oBAC1E,aAAa,CAAC,6CAA6C,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;YAED,MAAM,YAAY,GAChB,MAAM,KAAK,QAAQ;gBACjB,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC3B,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACxB,MAAM,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;gBAC1B,kEAAkE;gBAClE,MAAM,cAAc,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC9D,aAAa,CAAC,cAAc,CAAC,CAAC;YAChC,CAAC;iBAAM,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClC,8CAA8C;gBAC9C,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CACpC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,EAClC,CAAC,CACF,CAAC;gBACF,aAAa,CACX,+BAA+B,iBAAiB,CAAC,UAAU,CAAC,YAAY,CACzE,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;QAEF,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QAChE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QAChE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACxB,KAAK,GAAG,GAAG,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACzB,cAAc,CAAC,gCAAgC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;gBAC7D,IAAI,SAAS,EAAE,CAAC;oBACd,KAAK,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAChE,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC;wBACH,gDAAgD;wBAChD,iDAAiD;wBACjD,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;wBACpC,MAAM,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;wBACjD,IAAI,CAAC,MAAM,EAAE,CAAC;4BACZ,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;oBAAC,OAAO,EAAE,EAAE,CAAC;wBACZ,iEAAiE;wBACjE,IAAI,CAAC,MAAM;4BAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACrC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QAEpE,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YAChC,MAAM,GAAG,IAAI,CAAC;YACd,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAEvD,mDAAmD;YACnD,MAAM,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC;YAC9B,MAAM,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC;YAE9B,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAEhD,OAAO,CAAC;gBACN,SAAS,EAAE,WAAW;gBACtB,MAAM,EAAE,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9C,QAAQ,EAAE,IAAI;gBACd,MAAM;gBACN,KAAK;gBACL,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,6BAA6B,CACpC,WAAwB,EACxB,QAAgB,EAChB,UAAkB;IAElB,MAAM,YAAY,GAChB,UAAU,CAAC,MAAM,GAAG,iBAAiB;QACnC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,iBAAiB,CAAC,GAAG,mBAAmB;QAClE,CAAC,CAAC,UAAU,CAAC;IAEjB,WAAW,CAAC,UAAU,CAAC;QACrB,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE;YACL;gBACE,IAAI,EAAE;;EAEZ,QAAQ;;;;;EAKR,YAAY;OACP;aACA;SACF;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,CACtC,gBAAoD,EACpD,qBAEC,EACD,MAAwC,EACxC,cAAyC,EACzC,MAAc,EACd,WAAwB,EACxB,EAAE;IACF,MAAM,kBAAkB,GAAG,WAAW,CACpC,CAAC,QAAuB,EAAE,WAAwB,EAAW,EAAE;QAC7D,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxC,gBAAgB,CACd,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,EACtC,oBAAoB,CACrB,CAAC;QAEF,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC;QAC5C,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,gBAAgB,GAAG,QAAQ,CAAC;QAChC,IAAI,WAA+B,CAAC;QAEpC,2EAA2E;QAC3E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,IAAI,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAG,aAAa,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;YAC7E,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,WAAW,CAAC,CAAC;YAClD,8DAA8D;YAC9D,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrD,OAAO,IAAI,GAAG,CAAC;YACjB,CAAC;YACD,gBAAgB,GAAG,KAAK,OAAO,yBAAyB,WAAW,iBAAiB,CAAC;QACvF,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;YAChD,IAAI,cAAc,GAAG,CAAC,CAAC;YAEvB,cAAc,CAAC,gBAAgB,SAAS,KAAK,gBAAgB,EAAE,CAAC,CAAC;YACjE,mBAAmB,CACjB,gBAAgB,EAChB,SAAS,EACT,WAAW,EACX,CAAC,cAAc,EAAE,EAAE;gBACjB,6DAA6D;gBAC7D,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,GAAG,yBAAyB,EAAE,CAAC;oBAC5D,qBAAqB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;oBAC9D,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC9B,CAAC;YACH,CAAC,EACD,cAAc,CACf;iBACE,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,iFAAiF;gBACjF,yDAAyD;gBACzD,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBAE5B,IAAI,eAAe,GAAiC,MAAM,CAAC;gBAC3D,IAAI,WAAmB,CAAC;gBAExB,yFAAyF;gBACzF,2FAA2F;gBAC3F,UAAU;gBACV,IAAI,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC/B,WAAW;wBACT,uDAAuD,CAAC;gBAC5D,CAAC;qBAAM,CAAC;oBACN,WAAW;wBACT,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,8BAA8B,CAAC;gBAC3D,CAAC;gBAED,IAAI,WAAW,GAAG,WAAW,CAAC;gBAE9B,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,eAAe,GAAG,OAAO,CAAC;oBAC1B,WAAW,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,KAAK,WAAW,EAAE,CAAC;gBAC1D,CAAC;qBAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBAC1B,WAAW,GAAG,2BAA2B,WAAW,EAAE,CAAC;gBACzD,CAAC;qBAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBACzB,eAAe,GAAG,OAAO,CAAC;oBAC1B,WAAW,GAAG,iCAAiC,MAAM,CAAC,MAAM,MAAM,WAAW,EAAE,CAAC;gBAClF,CAAC;qBAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;oBACjC,eAAe,GAAG,OAAO,CAAC;oBAC1B,WAAW,GAAG,4BAA4B,MAAM,CAAC,QAAQ,MAAM,WAAW,EAAE,CAAC;gBAC/E,CAAC;gBAED,IAAI,WAAW,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC9C,MAAM,QAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC7D,IAAI,QAAQ,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;wBACvC,MAAM,OAAO,GAAG,8DAA8D,QAAQ,qBAAqB,CAAC;wBAC5G,WAAW,GAAG,GAAG,OAAO,OAAO,WAAW,EAAE,CAAC;oBAC/C,CAAC;gBACH,CAAC;gBAED,+DAA+D;gBAC/D,gBAAgB,CACd,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAE,EAC5C,oBAAoB,CACrB,CAAC;gBAEF,iEAAiE;gBACjE,6BAA6B,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YACpE,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACb,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBAC5B,MAAM,YAAY,GAChB,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACnD,gBAAgB,CACd;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,iCAAiC,YAAY,EAAE;iBACtD,EACD,oBAAoB,CACrB,CAAC;YACJ,CAAC,CAAC;iBACD,OAAO,CAAC,GAAG,EAAE;gBACZ,IAAI,WAAW,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC9C,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAC7B,CAAC;gBACD,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,WAAW,CAAC,CAAC;QACpB,OAAO,IAAI,CAAC,CAAC,wBAAwB;IACvC,CAAC,EACD;QACE,MAAM;QACN,cAAc;QACd,gBAAgB;QAChB,qBAAqB;QACrB,MAAM;QACN,WAAW;KACZ,CACF,CAAC;IAEF,OAAO,EAAE,kBAAkB,EAAE,CAAC;AAChC,CAAC,CAAC"}