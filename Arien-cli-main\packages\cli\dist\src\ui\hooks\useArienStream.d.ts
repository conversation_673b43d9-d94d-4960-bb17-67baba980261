/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Config, ArienClient, EditorType, ThoughtSummary } from '@arien/arien-cli-core';
import { type PartListUnion } from '@google/genai';
import { StreamingState, HistoryItem, HistoryItemWithoutId } from '../types.js';
import { UseHistoryManagerReturn } from './useHistoryManager.js';
export declare function mergePartListUnions(list: PartListUnion[]): PartListUnion;
/**
 * Manages the Arien AI stream, including user input, command processing,
 * response handling, and tool execution.
 */
export declare const useArienStream: (arienClient: ArienClient, history: HistoryItem[], addItem: UseHistoryManagerReturn["addItem"], setShowHelp: React.Dispatch<React.SetStateAction<boolean>>, config: Config, onDebugMessage: (message: string) => void, handleSlashCommand: (cmd: PartListUnion) => Promise<import("./slashCommandProcessor.js").SlashCommandActionReturn | boolean>, shellModeActive: boolean, getPreferredEditor: () => EditorType | undefined, onAuthError: () => void, performMemoryRefresh: () => Promise<void>) => {
    streamingState: StreamingState;
    submitQuery: (query: PartListUnion, options?: {
        isContinuation: boolean;
    }) => Promise<void>;
    initError: string | null;
    pendingHistoryItems: HistoryItemWithoutId[];
    thought: ThoughtSummary | null;
};
