import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { useStreamingContext } from '../contexts/StreamingContext.js';
import { StreamingState } from '../types.js';
import { ArienRespondingSpinner } from './ArienRespondingSpinner.js';
export const LoadingIndicator = ({ currentLoadingPhrase, elapsedTime, rightContent, thought, }) => {
    const streamingState = useStreamingContext();
    if (streamingState === StreamingState.Idle) {
        return null;
    }
    const primaryText = thought?.subject || currentLoadingPhrase;
    // More concise loading states
    const getLoadingState = () => {
        switch (streamingState) {
            case StreamingState.WaitingForConfirmation:
                return 'Waiting for confirmation';
            case StreamingState.Responding:
                return 'Processing';
            default:
                return 'Loading';
        }
    };
    // Simple progress indicator
    const getProgressDots = () => {
        const dots = Math.min(Math.floor(elapsedTime / 2), 3);
        return '.'.repeat(dots);
    };
    return (_jsxs(Box, { marginTop: 0, paddingLeft: 0, flexDirection: "row", alignItems: "center", children: [_jsx(Box, { marginRight: 1, children: _jsx(ArienRespondingSpinner, { nonRespondingDisplay: streamingState === StreamingState.WaitingForConfirmation
                        ? '?'
                        : '' }) }), _jsxs(Text, { color: Colors.AccentPurple, children: [primaryText || getLoadingState(), getProgressDots()] }), _jsx(Text, { color: Colors.Gray, children: streamingState === StreamingState.WaitingForConfirmation
                    ? ' (awaiting input)'
                    : ` (${elapsedTime}s)` }), _jsx(Box, { flexGrow: 1 }), rightContent && _jsx(Box, { children: rightContent })] }));
};
//# sourceMappingURL=LoadingIndicator.js.map