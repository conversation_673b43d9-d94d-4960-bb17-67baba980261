import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { useStreamingContext } from '../contexts/StreamingContext.js';
import { StreamingState } from '../types.js';
import { ArienRespondingSpinner } from './ArienRespondingSpinner.js';
export const LoadingIndicator = ({ currentLoadingPhrase, elapsedTime, rightContent, thought, }) => {
    const streamingState = useStreamingContext();
    if (streamingState === StreamingState.Idle) {
        return null;
    }
    const primaryText = thought?.subject || currentLoadingPhrase;
    // Enhanced loading state feedback with more detailed states
    const getLoadingStateInfo = () => {
        switch (streamingState) {
            case StreamingState.WaitingForConfirmation:
                return {
                    text: 'Waiting for confirmation',
                    color: Colors.AccentYellow,
                    icon: '⏳',
                    showCancel: false,
                };
            case StreamingState.Responding:
                return {
                    text: 'Processing response',
                    color: Colors.AccentPurple,
                    icon: '⚡',
                    showCancel: true,
                };
            default:
                return {
                    text: 'Loading',
                    color: Colors.AccentBlue,
                    icon: '◆',
                    showCancel: true,
                };
        }
    };
    // Enhanced progress indication based on elapsed time
    const getProgressInfo = () => {
        if (elapsedTime < 1)
            return { indicator: '', stage: 'starting' };
        if (elapsedTime < 3)
            return { indicator: '●', stage: 'initializing' };
        if (elapsedTime < 8)
            return { indicator: '●●', stage: 'processing' };
        if (elapsedTime < 15)
            return { indicator: '●●●', stage: 'working' };
        if (elapsedTime < 30)
            return { indicator: '●●●●', stage: 'complex task' };
        return { indicator: '●●●●●', stage: 'intensive processing' };
    };
    // Enhanced time-based messaging
    const getTimeBasedMessage = () => {
        if (elapsedTime < 5)
            return null;
        if (elapsedTime < 15)
            return 'Processing complex request...';
        if (elapsedTime < 30)
            return 'This is taking longer than usual...';
        if (elapsedTime < 60)
            return 'Complex operation in progress...';
        return 'This is a particularly complex task. Please wait...';
    };
    const loadingStateInfo = getLoadingStateInfo();
    const progressInfo = getProgressInfo();
    const timeBasedMessage = getTimeBasedMessage();
    return (_jsxs(Box, { flexDirection: "column", marginTop: 1, paddingX: 1, children: [_jsxs(Box, { borderStyle: "round", borderColor: loadingStateInfo.color, paddingX: 2, paddingY: 0, alignItems: "center", children: [_jsx(Box, { marginRight: 1, alignItems: "center", children: _jsx(ArienRespondingSpinner, { nonRespondingDisplay: streamingState === StreamingState.WaitingForConfirmation
                                ? loadingStateInfo.icon
                                : '' }) }), _jsxs(Box, { flexGrow: 1, alignItems: "center", children: [primaryText ? (_jsx(Text, { color: loadingStateInfo.color, bold: true, children: primaryText })) : (_jsx(Text, { color: loadingStateInfo.color, bold: true, children: loadingStateInfo.text })), progressInfo.indicator && (_jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: Colors.Gray, children: progressInfo.indicator }) }))] }), _jsxs(Box, { alignItems: "center", marginLeft: 1, children: [_jsxs(Text, { color: Colors.Gray, dimColor: true, children: [elapsedTime, "s"] }), loadingStateInfo.showCancel && (_jsxs(Text, { color: Colors.Gray, dimColor: true, children: [' ', "(esc to cancel)"] })), streamingState === StreamingState.WaitingForConfirmation && (_jsxs(Text, { color: Colors.AccentYellow, dimColor: true, children: [' ', "(awaiting input)"] }))] }), rightContent && (_jsx(Box, { marginLeft: 1, children: rightContent }))] }), (timeBasedMessage || progressInfo.stage !== 'starting') && (_jsxs(Box, { marginTop: 1, paddingLeft: 3, alignItems: "center", children: [_jsxs(Text, { color: Colors.AccentCyan, dimColor: true, children: ["\u25C6", ' '] }), _jsx(Text, { color: Colors.Gray, dimColor: true, children: timeBasedMessage || `Status: ${progressInfo.stage}` })] })), thought && thought.subject !== primaryText && (_jsxs(Box, { marginTop: 1, paddingLeft: 3, alignItems: "center", children: [_jsxs(Text, { color: Colors.AccentPurple, dimColor: true, children: ["\u25C7", ' '] }), _jsx(Text, { color: Colors.Gray, dimColor: true, children: thought.subject })] }))] }));
};
//# sourceMappingURL=LoadingIndicator.js.map