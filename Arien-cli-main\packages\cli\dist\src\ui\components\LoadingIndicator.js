import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { useStreamingContext } from '../contexts/StreamingContext.js';
import { StreamingState } from '../types.js';
import { ArienRespondingSpinner } from './ArienRespondingSpinner.js';
export const LoadingIndicator = ({ currentLoadingPhrase, elapsedTime, rightContent, thought, }) => {
    const streamingState = useStreamingContext();
    if (streamingState === StreamingState.Idle) {
        return null;
    }
    const primaryText = thought?.subject || currentLoadingPhrase;
    // Enhanced loading state feedback
    const getLoadingState = () => {
        switch (streamingState) {
            case StreamingState.WaitingForConfirmation:
                return 'Waiting for confirmation';
            case StreamingState.Responding:
                return 'Processing response';
            default:
                return 'Loading';
        }
    };
    // Progress indicator based on elapsed time
    const getProgressIndicator = () => {
        if (elapsedTime < 2)
            return '';
        if (elapsedTime < 5)
            return ' •';
        if (elapsedTime < 10)
            return ' ••';
        if (elapsedTime < 20)
            return ' •••';
        return ' ••••';
    };
    return (_jsxs(Box, { marginTop: 1, paddingLeft: 0, flexDirection: "column", children: [_jsxs(Box, { children: [_jsx(Box, { marginRight: 1, children: _jsx(ArienRespondingSpinner, { nonRespondingDisplay: streamingState === StreamingState.WaitingForConfirmation
                                ? '⠏'
                                : '' }) }), primaryText ? (_jsxs(Text, { color: Colors.AccentPurple, children: [primaryText, getProgressIndicator()] })) : (_jsxs(Text, { color: Colors.AccentPurple, children: [getLoadingState(), getProgressIndicator()] })), _jsx(Text, { color: Colors.Gray, children: streamingState === StreamingState.WaitingForConfirmation
                            ? ' (awaiting input)'
                            : ` (esc to cancel, ${elapsedTime}s)` }), _jsx(Box, { flexGrow: 1 }), rightContent && _jsx(Box, { children: rightContent })] }), elapsedTime > 10 && streamingState !== StreamingState.WaitingForConfirmation && (_jsx(Box, { marginTop: 0, paddingLeft: 3, children: _jsx(Text, { color: Colors.Gray, dimColor: true, children: elapsedTime > 30
                        ? 'This is taking longer than usual. The operation is still running...'
                        : 'Processing complex request...' }) }))] }));
};
//# sourceMappingURL=LoadingIndicator.js.map