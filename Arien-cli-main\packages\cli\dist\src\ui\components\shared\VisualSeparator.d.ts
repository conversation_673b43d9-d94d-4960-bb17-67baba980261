/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
interface VisualSeparatorProps {
    width?: number;
    style?: 'line' | 'dots' | 'dashes' | 'double';
    color?: string;
    marginY?: number;
    align?: 'flex-start' | 'center' | 'flex-end';
    text?: string;
}
export declare const VisualSeparator: React.FC<VisualSeparatorProps>;
export {};
