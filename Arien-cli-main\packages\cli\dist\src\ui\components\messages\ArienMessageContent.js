import { jsx as _jsx } from "react/jsx-runtime";
import { Box } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
/*
 * Arien message content is a semi-hacked component. The intention is to represent a partial
 * of ArienMessage and is only used when a response gets too long. In that instance messages
 * are split into multiple ArienMessageContent's to enable the root <Static> component in
 * App.tsx to be as performant as humanly possible.
 */
export const ArienMessageContent = ({ text, isPending, availableTerminalHeight, terminalWidth, }) => {
    // Use the same spacing as ArienMessage for consistent alignment
    // This ensures content aligns properly without showing duplicate icons
    return (_jsx(Box, { flexDirection: "column", paddingLeft: 2, children: _jsx(MarkdownDisplay, { text: text, isPending: isPending, availableTerminalHeight: availableTerminalHeight, terminalWidth: terminalWidth }) }));
};
//# sourceMappingURL=ArienMessageContent.js.map