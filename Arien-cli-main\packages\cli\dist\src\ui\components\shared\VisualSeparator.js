import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';
export const VisualSeparator = ({ width = 50, style = 'line', color = Colors.Gray, marginY = 1, align = 'center', text, }) => {
    const getCharacter = () => {
        switch (style) {
            case 'dots':
                return '•';
            case 'dashes':
                return '-';
            case 'double':
                return '═';
            case 'line':
            default:
                return '─';
        }
    };
    const character = getCharacter();
    const effectiveWidth = Math.max(10, width);
    if (text) {
        const textLength = text.length;
        const sideLength = Math.floor((effectiveWidth - textLength - 2) / 2);
        const leftSide = character.repeat(Math.max(0, sideLength));
        const rightSide = character.repeat(Math.max(0, effectiveWidth - textLength - 2 - sideLength));
        return (_jsx(Box, { marginY: marginY, justifyContent: align, width: "100%", children: _jsxs(Text, { color: color, dimColor: true, children: [leftSide, " ", text, " ", rightSide] }) }));
    }
    return (_jsx(Box, { marginY: marginY, justifyContent: align, width: "100%", children: _jsx(Text, { color: color, dimColor: true, children: character.repeat(effectiveWidth) }) }));
};
//# sourceMappingURL=VisualSeparator.js.map