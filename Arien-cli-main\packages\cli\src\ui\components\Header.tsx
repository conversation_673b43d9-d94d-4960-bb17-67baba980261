/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import Gradient from 'ink-gradient';
import { Colors } from '../colors.js';
import { shortAsciiLogo, longAsciiLogo } from './AsciiArt.js';
import { getAsciiArtWidth } from '../utils/textUtils.js';

interface HeaderProps {
  customAsciiArt?: string; // For user-defined ASCII art
  terminalWidth: number; // For responsive logo
}

export const Header: React.FC<HeaderProps> = ({
  customAsciiArt,
  terminalWidth,
}) => {
  let displayTitle;
  const widthOfLongLogo = getAsciiArtWidth(longAsciiLogo);
  const widthOfShortLogo = getAsciiArtWidth(shortAsciiLogo);

  if (customAsciiArt) {
    displayTitle = customAsciiArt;
  } else {
    // Enhanced responsive logo selection with more graceful fallbacks
    if (terminalWidth >= widthOfLongLogo + 20) {
      displayTitle = longAsciiLogo;
    } else if (terminalWidth >= widthOfShortLogo + 10) {
      displayTitle = shortAsciiLogo;
    } else if (terminalWidth >= 30) {
      // Compact fallback for medium terminals
      displayTitle = 'ARIEN AI';
    } else {
      // Minimal fallback for very narrow terminals
      displayTitle = 'Arien';
    }
  }

  const artWidth = getAsciiArtWidth(displayTitle);

  return (
    <Box
      marginBottom={1}
      alignItems="center"
      justifyContent="center"
      width="100%"
      flexShrink={0}
      paddingBottom={1}
    >
      <Box
        paddingX={1}
        paddingY={0}
        alignItems="center"
        justifyContent="center"
      >
        {Colors.GradientColors ? (
          <Gradient colors={Colors.GradientColors}>
            <Text>{displayTitle}</Text>
          </Gradient>
        ) : (
          <Text color={Colors.AccentPurple} bold={true}>
            {displayTitle}
          </Text>
        )}
      </Box>
      <Box
        marginTop={1}
        width="100%"
        justifyContent="center"
      >
        <Text color={Colors.Gray}>
          {'─'.repeat(Math.min(terminalWidth - 4, Math.max(artWidth, 20)))}
        </Text>
      </Box>
    </Box>
  );
};
