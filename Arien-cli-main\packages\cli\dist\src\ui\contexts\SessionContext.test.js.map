{"version": 3, "file": "SessionContext.test.js", "sourceRoot": "", "sources": ["../../../../src/ui/contexts/SessionContext.test.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAE,GAAG,EAAE,MAAM,sBAAsB,CAAC;AAC3C,OAAO,EAAE,oBAAoB,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAC5E,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC;AAGlD,6DAA6D;AAC7D,MAAM,aAAa,GAAyC;IAC1D,gBAAgB,EAAE,GAAG;IACrB,oBAAoB,EAAE,GAAG;IACzB,eAAe,EAAE,GAAG;IACpB,uBAAuB,EAAE,EAAE;IAC3B,uBAAuB,EAAE,EAAE;IAC3B,kBAAkB,EAAE,EAAE;CACvB,CAAC;AAEF,MAAM,aAAa,GAAyC;IAC1D,gBAAgB,EAAE,EAAE;IACpB,oBAAoB,EAAE,EAAE;IACxB,eAAe,EAAE,EAAE;IACnB,uBAAuB,EAAE,CAAC;IAC1B,uBAAuB,EAAE,CAAC;IAC1B,kBAAkB,EAAE,CAAC;CACtB,CAAC;AAEF;;;;GAIG;AACH,MAAM,WAAW,GAAG,CAAC,EACnB,UAAU,GAGX,EAAE,EAAE;IACH,UAAU,CAAC,OAAO,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;QAClD,MAAM,UAAU,GAEZ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAE3B,MAAM,CACJ,KAAC,oBAAoB,cACnB,KAAC,WAAW,IAAC,UAAU,EAAE,UAAU,GAAI,GAClB,CACxB,CAAC;QAEF,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QAExC,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACzC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;QAChE,MAAM,UAAU,GAEZ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAE3B,MAAM,CACJ,KAAC,oBAAoB,cACnB,KAAC,WAAW,IAAC,UAAU,EAAE,UAAU,GAAI,GAClB,CACxB,CAAC;QAEF,GAAG,CAAC,GAAG,EAAE;YACP,UAAU,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QACxC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5C,qCAAqC;QACrC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;QACxE,MAAM,UAAU,GAEZ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAE3B,MAAM,CACJ,KAAC,oBAAoB,cACnB,KAAC,WAAW,IAAC,UAAU,EAAE,UAAU,GAAI,GAClB,CACxB,CAAC;QAEF,GAAG,CAAC,GAAG,EAAE;YACP,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QAExC,sCAAsC;QACtC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAC5C,aAAa,CAAC,eAAe,IAAI,CAAC,CACnC,CAAC;QACF,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAC7C,aAAa,CAAC,gBAAgB,IAAI,CAAC,CACpC,CAAC;QACF,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE9C,2CAA2C;QAC3C,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE5C,oCAAoC;QACpC,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC,OAAO,CACjD,aAAa,CAAC,eAAe,CAC9B,CAAC;QACF,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;QAC5E,MAAM,UAAU,GAEZ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAE3B,MAAM,CACJ,KAAC,oBAAoB,cACnB,KAAC,WAAW,IAAC,UAAU,EAAE,UAAU,GAAI,GAClB,CACxB,CAAC;QAEF,4BAA4B;QAC5B,GAAG,CAAC,GAAG,EAAE;YACP,UAAU,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,uDAAuD;QACvD,GAAG,CAAC,GAAG,EAAE;YACP,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,wDAAwD;QACxD,GAAG,CAAC,GAAG,EAAE;YACP,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QAExC,8BAA8B;QAC9B,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE5C,iCAAiC;QACjC,+CAA+C;QAC/C,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QACzD,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC9D,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC1D,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAEnD,+CAA+C;QAC/C,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC1D,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC/D,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAE/D,mCAAmC;QACnC,0DAA0D;QAC1D,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC1D,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC/D,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3D,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC3D,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAChE,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAChE,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;QAC7D,MAAM,UAAU,GAEZ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAE3B,MAAM,CACJ,KAAC,oBAAoB,cACnB,KAAC,WAAW,IAAC,UAAU,EAAE,UAAU,GAAI,GAClB,CACxB,CAAC;QAEF,oBAAoB;QACpB,GAAG,CAAC,GAAG,EAAE;YACP,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QAEtC,8CAA8C;QAC9C,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzD,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEnD,qBAAqB;QACrB,GAAG,CAAC,GAAG,EAAE;YACP,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QAElC,mDAAmD;QACnD,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxD,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAElD,sBAAsB;QACtB,GAAG,CAAC,GAAG,EAAE;YACP,UAAU,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;QAElC,kCAAkC;QAClC,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvD,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0EAA0E,EAAE,GAAG,EAAE;QAClF,gEAAgE;QAChE,MAAM,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAE3E,IAAI,CAAC;YACH,6EAA6E;YAC7E,MAAM,CAAC,GAAG,EAAE;gBACV,UAAU,CAAC,GAAG,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,OAAO,CAAC,4DAA4D,CAAC,CAAC;QAC3E,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}