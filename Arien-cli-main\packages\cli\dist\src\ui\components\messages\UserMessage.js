import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Text, Box } from 'ink';
import { Colors } from '../../colors.js';
export const UserMessage = ({ text }) => {
    const prefix = '> ';
    const prefixWidth = prefix.length;
    return (_jsxs(Box, { flexDirection: "row", marginY: 1, children: [_jsx(Box, { width: prefixWidth, children: _jsx(Text, { color: Colors.AccentBlue, bold: true, children: prefix }) }), _jsx(Box, { flexGrow: 1, children: _jsx(Text, { wrap: "wrap", color: Colors.Foreground, children: text }) })] }));
};
//# sourceMappingURL=UserMessage.js.map