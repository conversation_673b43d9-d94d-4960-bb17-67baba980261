import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Text, Box } from 'ink';
import { Colors } from '../../colors.js';
export const UserMessage = ({ text }) => (_jsx(Box, { marginY: 1, children: _jsxs(Box, { borderStyle: "round", borderColor: Colors.AccentBlue, paddingX: 2, paddingY: 0, width: "100%", children: [_jsxs(Box, { marginRight: 2, alignItems: "center", minWidth: "12", children: [_jsx(Text, { color: Colors.AccentBlue, bold: true, children: "\u25C6" }), _jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: Colors.AccentBlue, bold: true, children: "You" }) })] }), _jsx(Box, { flexGrow: 1, minWidth: "0", children: _jsx(Text, { wrap: "wrap", color: Colors.Foreground, children: text }) })] }) }));
//# sourceMappingURL=UserMessage.js.map