import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const MAX_SUGGESTIONS_TO_SHOW = 8;
export function SuggestionsDisplay({ suggestions, activeIndex, isLoading, width, scrollOffset, userInput, }) {
    if (isLoading) {
        return (_jsxs(Box, { borderStyle: "round", borderColor: Colors.AccentCyan, paddingX: 2, paddingY: 1, width: width, children: [_jsx(Text, { color: Colors.AccentCyan, children: "\u25C6 " }), _jsx(Text, { color: Colors.Gray, children: "Loading suggestions..." })] }));
    }
    if (suggestions.length === 0) {
        return null; // Don't render anything if there are no suggestions
    }
    // Calculate the visible slice based on scrollOffset
    const startIndex = scrollOffset;
    const endIndex = Math.min(scrollOffset + MAX_SUGGESTIONS_TO_SHOW, suggestions.length);
    const visibleSuggestions = suggestions.slice(startIndex, endIndex);
    return (_jsxs(Box, { flexDirection: "column", borderStyle: "round", borderColor: Colors.AccentCyan, paddingX: 1, paddingY: 1, width: width, children: [_jsxs(Box, { marginBottom: 1, justifyContent: "space-between", alignItems: "center", children: [_jsxs(Box, { alignItems: "center", children: [_jsx(Text, { color: Colors.AccentCyan, bold: true, children: "\u25C6 Suggestions" }), userInput.startsWith('/') && (_jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: Colors.Gray, dimColor: true, children: "(commands)" }) })), userInput.includes('@') && (_jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: Colors.Gray, dimColor: true, children: "(files)" }) }))] }), suggestions.length > MAX_SUGGESTIONS_TO_SHOW && (_jsxs(Text, { color: Colors.Gray, dimColor: true, children: [activeIndex + 1, "/", suggestions.length] }))] }), scrollOffset > 0 && (_jsx(Box, { justifyContent: "center", marginBottom: 1, children: _jsx(Text, { color: Colors.AccentCyan, children: "\u25B2 more above" }) })), visibleSuggestions.map((suggestion, index) => {
                const originalIndex = startIndex + index;
                const isActive = originalIndex === activeIndex;
                const textColor = isActive ? Colors.AccentPurple : Colors.Foreground;
                const bgChar = isActive ? '◆' : '◇';
                return (_jsx(Box, { marginBottom: 0, children: _jsxs(Box, { alignItems: "center", width: "100%", children: [_jsxs(Text, { color: isActive ? Colors.AccentPurple : Colors.Gray, children: [bgChar, ' '] }), userInput.startsWith('/') ? (
                            // Enhanced command mode display
                            _jsxs(Box, { flexDirection: "row", width: "100%", children: [_jsx(Box, { width: 20, flexShrink: 0, children: _jsx(Text, { color: textColor, bold: isActive, children: suggestion.label }) }), suggestion.description && (_jsx(Box, { flexGrow: 1, marginLeft: 1, children: _jsx(Text, { color: isActive ? Colors.Gray : Colors.Comment, wrap: "wrap", children: suggestion.description }) }))] })) : (
                            // Enhanced file mode display
                            _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { color: textColor, bold: isActive, children: suggestion.label }), suggestion.description && (_jsx(Text, { color: isActive ? Colors.Gray : Colors.Comment, dimColor: true, wrap: "wrap", children: suggestion.description }))] }))] }) }, `${suggestion.label}-${originalIndex}`));
            }), endIndex < suggestions.length && (_jsx(Box, { justifyContent: "center", marginTop: 1, children: _jsx(Text, { color: Colors.AccentCyan, children: "\u25BC more below" }) })), _jsx(Box, { marginTop: 1, justifyContent: "center", children: _jsx(Text, { color: Colors.Gray, dimColor: true, children: "\u2191\u2193 navigate \u2022 tab/enter select \u2022 esc cancel" }) })] }));
}
//# sourceMappingURL=SuggestionsDisplay.js.map