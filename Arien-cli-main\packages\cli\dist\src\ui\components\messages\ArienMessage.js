import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Text, Box } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
import { Colors } from '../../colors.js';
import { AnimatedIcon } from '../shared/AnimatedIcon.js';
export const ArienMessage = ({ text, isPending, availableTerminalHeight, terminalWidth, }) => {
    return (_jsx(Box, { marginY: 1, children: _jsxs(Box, { borderStyle: "round", borderColor: isPending ? Colors.AccentPurple : Colors.Gray, paddingX: 2, paddingY: 0, width: "100%", children: [_jsxs(Box, { marginRight: 2, alignItems: "center", minWidth: "12", children: [_jsx(AnimatedIcon, { isPending: isPending, color: Colors.AccentPurple, animationType: isPending ? 'pulse' : 'default' }), _jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: Colors.AccentPurple, bold: true, children: "Arien" }) }), isPending && (_jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: Colors.Gray, dimColor: true, children: "(thinking...)" }) }))] }), _jsxs(Box, { flexGrow: 1, flexDirection: "column", minWidth: "0", children: [_jsx(MarkdownDisplay, { text: text, isPending: isPending, availableTerminalHeight: availableTerminalHeight, terminalWidth: Math.max(40, terminalWidth - 20) }), isPending && text.trim() === '' && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { color: Colors.Gray, dimColor: true, italic: true, children: "Preparing response..." }) }))] })] }) }));
};
//# sourceMappingURL=ArienMessage.js.map