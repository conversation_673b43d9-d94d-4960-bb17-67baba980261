{"version": 3, "file": "useInputHistory.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useInputHistory.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAgB9C,MAAM,UAAU,eAAe,CAAC,EAC9B,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,QAAQ,GACa;IACrB,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAS,CAAC,CAAC,CAAC,CAAC;IAC7D,MAAM,CAAC,sBAAsB,EAAE,yBAAyB,CAAC,GACvD,QAAQ,CAAS,EAAE,CAAC,CAAC;IAEvB,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;QACvC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,yBAAyB,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,YAAY,GAAG,WAAW,CAC9B,CAAC,KAAa,EAAE,EAAE;QAChB,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAClC,IAAI,YAAY,EAAE,CAAC;YACjB,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,oCAAoC;QAC9D,CAAC;QACD,eAAe,EAAE,CAAC;IACpB,CAAC,EACD,CAAC,QAAQ,EAAE,eAAe,CAAC,CAC5B,CAAC;IAEF,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;QAClC,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QAC5B,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAE5C,IAAI,SAAS,GAAG,YAAY,CAAC;QAC7B,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;YACxB,4DAA4D;YAC5D,yBAAyB,CAAC,YAAY,CAAC,CAAC;YACxC,SAAS,GAAG,CAAC,CAAC;QAChB,CAAC;aAAM,IAAI,YAAY,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC,CAAC,gCAAgC;QAChD,CAAC;QAED,IAAI,SAAS,KAAK,YAAY,EAAE,CAAC;YAC/B,eAAe,CAAC,SAAS,CAAC,CAAC;YAC3B,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;YACnE,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,EAAE;QACD,YAAY;QACZ,eAAe;QACf,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,YAAY,EAAE,8BAA8B;QAC5C,yBAAyB;KAC1B,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QAC5B,IAAI,YAAY,KAAK,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC,CAAC,mCAAmC;QAE1E,MAAM,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC;QACnC,eAAe,CAAC,SAAS,CAAC,CAAC;QAE3B,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YACrB,gEAAgE;YAChE,QAAQ,CAAC,sBAAsB,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;YACnE,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,EAAE;QACD,YAAY;QACZ,eAAe;QACf,sBAAsB;QACtB,QAAQ;QACR,YAAY;QACZ,QAAQ;KACT,CAAC,CAAC;IAEH,OAAO;QACL,YAAY;QACZ,UAAU;QACV,YAAY;KACb,CAAC;AACJ,CAAC"}