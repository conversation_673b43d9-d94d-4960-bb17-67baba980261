{"version": 3, "file": "SuggestionsDisplay.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/SuggestionsDisplay.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAetC,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,CAAC;AAEzC,MAAM,UAAU,kBAAkB,CAAC,EACjC,WAAW,EACX,WAAW,EACX,SAAS,EACT,KAAK,EACL,YAAY,EACZ,SAAS,GACe;IACxB,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,UAAU,EAC9B,QAAQ,EAAE,CAAC,EACX,QAAQ,EAAE,CAAC,EACX,KAAK,EAAE,KAAK,aAEZ,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,wBAAW,EACzC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,uCAA+B,IACnD,CACP,CAAC;IACJ,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC,CAAC,oDAAoD;IACnE,CAAC;IAED,oDAAoD;IACpD,MAAM,UAAU,GAAG,YAAY,CAAC;IAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACvB,YAAY,GAAG,uBAAuB,EACtC,WAAW,CAAC,MAAM,CACnB,CAAC;IACF,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAEnE,OAAO,CACL,MAAC,GAAG,IACF,aAAa,EAAC,QAAQ,EACtB,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,UAAU,EAC9B,QAAQ,EAAE,CAAC,EACX,QAAQ,EAAE,CAAC,EACX,KAAK,EAAE,KAAK,aAGZ,MAAC,GAAG,IAAC,YAAY,EAAE,CAAC,EAAE,cAAc,EAAC,eAAe,EAAC,UAAU,EAAC,QAAQ,aACtE,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,aACtB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,IAAI,yCAAqB,EACxD,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAC5B,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,iCAAkB,GAChD,CACP,EACA,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAC1B,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,8BAAe,GAC7C,CACP,IACG,EAEL,WAAW,CAAC,MAAM,GAAG,uBAAuB,IAAI,CAC/C,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,mBAC/B,WAAW,GAAG,CAAC,OAAG,WAAW,CAAC,MAAM,IAChC,CACR,IACG,EAGL,YAAY,GAAG,CAAC,IAAI,CACnB,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,YAC1C,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,kCAAqB,GAC/C,CACP,EAGA,kBAAkB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;gBAC5C,MAAM,aAAa,GAAG,UAAU,GAAG,KAAK,CAAC;gBACzC,MAAM,QAAQ,GAAG,aAAa,KAAK,WAAW,CAAC;gBAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;gBACrE,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBAEpC,OAAO,CACL,KAAC,GAAG,IAA8C,YAAY,EAAE,CAAC,YAC/D,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,aAEnC,MAAC,IAAI,IAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,aACtD,MAAM,EAAE,GAAG,IACP,EAEN,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;4BAC3B,gCAAgC;4BAChC,MAAC,GAAG,IAAC,aAAa,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,aACnC,KAAC,GAAG,IAAC,KAAK,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,YAC3B,KAAC,IAAI,IAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,YACnC,UAAU,CAAC,KAAK,GACZ,GACH,EACL,UAAU,CAAC,WAAW,IAAI,CACzB,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,YAC7B,KAAC,IAAI,IAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAC,MAAM,YAC9D,UAAU,CAAC,WAAW,GAClB,GACH,CACP,IACG,CACP,CAAC,CAAC,CAAC;4BACF,6BAA6B;4BAC7B,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,aACtC,KAAC,IAAI,IAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,YACnC,UAAU,CAAC,KAAK,GACZ,EACN,UAAU,CAAC,WAAW,IAAI,CACzB,KAAC,IAAI,IAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,QAAC,IAAI,EAAC,MAAM,YACvE,UAAU,CAAC,WAAW,GAClB,CACR,IACG,CACP,IACG,IApCE,GAAG,UAAU,CAAC,KAAK,IAAI,aAAa,EAAE,CAqC1C,CACP,CAAC;YACJ,CAAC,CAAC,EAGD,QAAQ,GAAG,WAAW,CAAC,MAAM,IAAI,CAChC,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,YACvC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,kCAAqB,GAC/C,CACP,EAGD,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,cAAc,EAAC,QAAQ,YACxC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,sFAE3B,GACH,IACF,CACP,CAAC;AACJ,CAAC"}