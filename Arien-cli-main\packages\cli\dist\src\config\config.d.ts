/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Config, FileDiscoveryService } from '@arien/arien-cli-core';
import { Settings } from './settings.js';
import { Extension } from './extension.js';
export declare function loadHierarchicalArienMemory(currentWorkingDirectory: string, debugMode: boolean, fileService: FileDiscoveryService, extensionContextFilePaths?: string[]): Promise<{
    memoryContent: string;
    fileCount: number;
}>;
export declare function loadCliConfig(settings: Settings, extensions: Extension[], sessionId: string): Promise<Config>;
export declare function loadEnvironment(): void;
