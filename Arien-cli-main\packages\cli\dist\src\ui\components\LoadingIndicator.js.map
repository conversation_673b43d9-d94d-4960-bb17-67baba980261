{"version": 3, "file": "LoadingIndicator.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/LoadingIndicator.tsx"], "names": [], "mappings": ";AAQA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AACtE,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AASrE,MAAM,CAAC,MAAM,gBAAgB,GAAoC,CAAC,EAChE,oBAAoB,EACpB,WAAW,EACX,YAAY,EACZ,OAAO,GACR,EAAE,EAAE;IACH,MAAM,cAAc,GAAG,mBAAmB,EAAE,CAAC;IAE7C,IAAI,cAAc,KAAK,cAAc,CAAC,IAAI,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,WAAW,GAAG,OAAO,EAAE,OAAO,IAAI,oBAAoB,CAAC;IAE7D,kCAAkC;IAClC,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,QAAQ,cAAc,EAAE,CAAC;YACvB,KAAK,cAAc,CAAC,sBAAsB;gBACxC,OAAO,0BAA0B,CAAC;YACpC,KAAK,cAAc,CAAC,UAAU;gBAC5B,OAAO,qBAAqB,CAAC;YAC/B;gBACE,OAAO,SAAS,CAAC;QACrB,CAAC;IACH,CAAC,CAAC;IAEF,2CAA2C;IAC3C,MAAM,oBAAoB,GAAG,GAAG,EAAE;QAChC,IAAI,WAAW,GAAG,CAAC;YAAE,OAAO,EAAE,CAAC;QAC/B,IAAI,WAAW,GAAG,CAAC;YAAE,OAAO,IAAI,CAAC;QACjC,IAAI,WAAW,GAAG,EAAE;YAAE,OAAO,KAAK,CAAC;QACnC,IAAI,WAAW,GAAG,EAAE;YAAE,OAAO,MAAM,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aAEvD,MAAC,GAAG,eACF,KAAC,GAAG,IAAC,WAAW,EAAE,CAAC,YACjB,KAAC,sBAAsB,IACrB,oBAAoB,EAClB,cAAc,KAAK,cAAc,CAAC,sBAAsB;gCACtD,CAAC,CAAC,GAAG;gCACL,CAAC,CAAC,EAAE,GAER,GACE,EACL,WAAW,CAAC,CAAC,CAAC,CACb,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,aAAG,WAAW,EAAE,oBAAoB,EAAE,IAAQ,CAC/E,CAAC,CAAC,CAAC,CACF,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,aAAG,eAAe,EAAE,EAAE,oBAAoB,EAAE,IAAQ,CACrF,EACD,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,YACrB,cAAc,KAAK,cAAc,CAAC,sBAAsB;4BACvD,CAAC,CAAC,mBAAmB;4BACrB,CAAC,CAAC,oBAAoB,WAAW,IAAI,GAClC,EACP,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,GAAsB,EACrC,YAAY,IAAI,KAAC,GAAG,cAAE,YAAY,GAAO,IACtC,EAGL,WAAW,GAAG,EAAE,IAAI,cAAc,KAAK,cAAc,CAAC,sBAAsB,IAAI,CAC/E,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,YAC/B,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,kBAC/B,WAAW,GAAG,EAAE;wBACf,CAAC,CAAC,qEAAqE;wBACvE,CAAC,CAAC,+BAA+B,GAE9B,GACH,CACP,IACG,CACP,CAAC;AACJ,CAAC,CAAC"}