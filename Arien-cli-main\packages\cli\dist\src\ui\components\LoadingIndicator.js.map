{"version": 3, "file": "LoadingIndicator.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/LoadingIndicator.tsx"], "names": [], "mappings": ";AAQA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AACtE,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AASrE,MAAM,CAAC,MAAM,gBAAgB,GAAoC,CAAC,EAChE,oBAAoB,EACpB,WAAW,EACX,YAAY,EACZ,OAAO,GACR,EAAE,EAAE;IACH,MAAM,cAAc,GAAG,mBAAmB,EAAE,CAAC;IAE7C,IAAI,cAAc,KAAK,cAAc,CAAC,IAAI,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,WAAW,GAAG,OAAO,EAAE,OAAO,IAAI,oBAAoB,CAAC;IAE7D,8BAA8B;IAC9B,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,QAAQ,cAAc,EAAE,CAAC;YACvB,KAAK,cAAc,CAAC,sBAAsB;gBACxC,OAAO,0BAA0B,CAAC;YACpC,KAAK,cAAc,CAAC,UAAU;gBAC5B,OAAO,YAAY,CAAC;YACtB;gBACE,OAAO,SAAS,CAAC;QACrB,CAAC;IACH,CAAC,CAAC;IAEF,4BAA4B;IAC5B,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAC,KAAK,EAAC,UAAU,EAAC,QAAQ,aACxE,KAAC,GAAG,IAAC,WAAW,EAAE,CAAC,YACjB,KAAC,sBAAsB,IACrB,oBAAoB,EAClB,cAAc,KAAK,cAAc,CAAC,sBAAsB;wBACtD,CAAC,CAAC,GAAG;wBACL,CAAC,CAAC,EAAE,GAER,GACE,EACN,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,aAC7B,WAAW,IAAI,eAAe,EAAE,EAAE,eAAe,EAAE,IAC/C,EACP,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,YACrB,cAAc,KAAK,cAAc,CAAC,sBAAsB;oBACvD,CAAC,CAAC,mBAAmB;oBACrB,CAAC,CAAC,KAAK,WAAW,IAAI,GACnB,EACP,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,GAAsB,EACrC,YAAY,IAAI,KAAC,GAAG,cAAE,YAAY,GAAO,IACtC,CACP,CAAC;AACJ,CAAC,CAAC"}