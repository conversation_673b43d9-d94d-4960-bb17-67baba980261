{"version": 3, "file": "Footer.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/Footer.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAC9E,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAiB7D,MAAM,CAAC,MAAM,MAAM,GAA0B,CAAC,EAC5C,KAAK,EACL,SAAS,EACT,UAAU,EACV,SAAS,EACT,YAAY,EACZ,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,eAAe,GAChB,EAAE,EAAE;IACH,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAChC,MAAM,UAAU,GAAG,eAAe,GAAG,KAAK,CAAC;IAC3C,MAAM,WAAW,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAExD,qEAAqE;IACrE,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,UAAU,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC,SAAS,CAAC;QAC9C,IAAI,UAAU,GAAG,IAAI;YAAE,OAAO,MAAM,CAAC,YAAY,CAAC;QAClD,IAAI,UAAU,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC,UAAU,CAAC;QAC/C,OAAO,MAAM,CAAC,WAAW,CAAC;IAC5B,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,GAAG,EAAE;QAC/B,IAAI,UAAU,GAAG,GAAG;YAAE,OAAO,aAAa,CAAC;QAC3C,IAAI,UAAU,GAAG,IAAI;YAAE,OAAO,SAAS,CAAC;QACxC,IAAI,UAAU,GAAG,GAAG;YAAE,OAAO,WAAW,CAAC;QACzC,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC;IAEF,wDAAwD;IACxD,MAAM,gBAAgB,GAAG,GAAG,EAAE;QAC5B,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;YAClE,OAAO;gBACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBACzD,KAAK,EAAE,MAAM,CAAC,WAAW;gBACzB,IAAI,EAAE,GAAG;aACV,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;YAClD,OAAO;gBACL,IAAI,EAAE,mBAAmB,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG;gBACxD,KAAK,EAAE,MAAM,CAAC,YAAY;gBAC1B,IAAI,EAAE,GAAG;aACV,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,MAAM,CAAC,SAAS;gBACvB,IAAI,EAAE,GAAG;aACV,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;IAEzC,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,SAAS,EAAE,CAAC,aAEnD,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,EAAE,KAAK,EAAC,MAAM,EAAC,cAAc,EAAC,QAAQ,YACxD,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,YACrB,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GACV,GACH,EAGN,MAAC,GAAG,IAAC,cAAc,EAAC,eAAe,EAAC,KAAK,EAAC,MAAM,EAAC,UAAU,EAAC,YAAY,aAEtE,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,aACxC,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,aACtB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,IAAI,8BAAU,EAC9C,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,YAC1B,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,GACpC,EACN,UAAU,IAAI,CACb,MAAC,GAAG,IAAC,UAAU,EAAE,CAAC,aAChB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,uBAAU,EACxC,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,kBAAI,UAAU,IAAQ,IAC1C,CACP,IACG,EACL,SAAS,IAAI,CACZ,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,UAAU,EAAC,QAAQ,aACpC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,IAAI,8BAAU,EAC7C,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,QAAQ,kBACpC,YAAY,IAAI,mBAAmB,GAC/B,IACH,CACP,IACG,EAGN,MAAC,GAAG,IACF,QAAQ,EAAE,CAAC,EACX,UAAU,EAAC,QAAQ,EACnB,cAAc,EAAC,QAAQ,EACvB,QAAQ,EAAC,KAAK,aAEd,MAAC,IAAI,IAAC,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,mBACnC,aAAa,CAAC,IAAI,EAAE,GAAG,IACnB,EACP,KAAC,IAAI,IAAC,KAAK,EAAE,aAAa,CAAC,KAAK,YAC7B,aAAa,CAAC,IAAI,GACd,IACH,EAGN,MAAC,GAAG,IAAC,UAAU,EAAC,UAAU,EAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,aAE9D,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,EAAC,cAAc,EAAC,UAAU,aAChD,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,IAAI,kBACjC,KAAK,GACD,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,eAAe,EAAE,EAAE,IAAI,mBACjC,GAAG,OAAG,WAAW,YAAQ,mBAAmB,EAAE,SAC1C,IACH,EAGN,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,EAAC,cAAc,EAAC,UAAU,EAAC,SAAS,EAAE,CAAC,aAC5D,SAAS,IAAI,CACZ,MAAC,GAAG,IAAC,WAAW,EAAE,CAAC,aACjB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,uBAAU,EACvC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,wBAAW,EACzC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,uBAAU,EACvC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,mBAAW,EACzC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,uBAAU,IACnC,CACP,EACA,CAAC,gBAAgB,IAAI,UAAU,GAAG,CAAC,IAAI,CACtC,KAAC,GAAG,IAAC,WAAW,EAAE,CAAC,YACjB,KAAC,qBAAqB,IAAC,UAAU,EAAE,UAAU,GAAI,GAC7C,CACP,EACA,eAAe,IAAI,CAClB,KAAC,GAAG,cACF,KAAC,kBAAkB,KAAG,GAClB,CACP,IACG,IACF,IACF,IACF,CACP,CAAC;AACJ,CAAC,CAAC"}