{"version": 3, "file": "Footer.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/Footer.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAE9E,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAiB7D,MAAM,CAAC,MAAM,MAAM,GAA0B,CAAC,EAC5C,KAAK,EACL,SAAS,EACT,UAAU,EACV,SAAS,EACT,YAAY,EACZ,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,oBAAoB,EACpB,eAAe,GAChB,EAAE,EAAE;IACH,MAAM,kBAAkB,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAC5B,CAAC,CAAC,kBAAkB,GAAG,eAAe,CAAC,GAAG,kBAAkB,CAAC,GAAG,GAAG,CACpE,CAAC;IAEF,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,WAAW,GAAG,EAAE;YAAE,OAAO,MAAM,CAAC,WAAW,CAAC;QAChD,IAAI,WAAW,GAAG,EAAE;YAAE,OAAO,MAAM,CAAC,YAAY,CAAC;QACjD,OAAO,MAAM,CAAC,SAAS,CAAC;IAC1B,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,GAAG,EAAE;QAC/B,IAAI,WAAW,GAAG,EAAE;YAAE,OAAO,QAAQ,CAAC;QACtC,IAAI,WAAW,GAAG,EAAE;YAAE,OAAO,WAAW,CAAC;QACzC,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;IAEF,yCAAyC;IACzC,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7D,MAAM,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,WAAW,IAAI,UAAU,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;IAEjF,gCAAgC;IAChC,MAAM,gBAAgB,GAAG,GAAG,EAAE;QAC5B,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;YAClE,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;aAAM,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;YAClD,OAAO,SAAS,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,OAAO,YAAY,CAAC;QACtB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;IACzC,MAAM,WAAW,GAAG,aAAa,KAAK,YAAY,CAAC;IAEnD,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,QAAQ,EAAE,CAAC,EACX,aAAa,EAAC,KAAK,EACnB,cAAc,EAAC,eAAe,EAC9B,UAAU,EAAC,QAAQ,aAGnB,KAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,YACtB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YAAG,cAAc,GAAQ,GACnD,EAGN,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,aACtB,KAAC,IAAI,IAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,YAC7D,aAAa,GACT,EACN,WAAW,IAAI,CACd,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,6BAAqB,CAC9C,IACG,EAGN,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,aACtB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YAAG,KAAK,GAAQ,EAC9C,MAAC,IAAI,IAAC,KAAK,EAAE,eAAe,EAAE,aAC3B,GAAG,OAAG,WAAW,YAAQ,mBAAmB,EAAE,SAC1C,EACN,CAAC,gBAAgB,IAAI,UAAU,GAAG,CAAC,IAAI,CACtC,MAAC,GAAG,IAAC,UAAU,EAAE,CAAC,aAChB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,kBAAU,EAClC,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,kBAAI,UAAU,YAAY,IACnD,CACP,EACA,SAAS,IAAI,CACZ,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,yBAAiB,CAC/C,EACA,eAAe,IAAI,KAAC,kBAAkB,KAAG,IACtC,IACF,CACP,CAAC;AACJ,CAAC,CAAC"}