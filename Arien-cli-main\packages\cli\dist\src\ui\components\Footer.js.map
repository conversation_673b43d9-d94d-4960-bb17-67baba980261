{"version": 3, "file": "Footer.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/Footer.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAC9E,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAiB7D,MAAM,CAAC,MAAM,MAAM,GAA0B,CAAC,EAC5C,KAAK,EACL,SAAS,EACT,UAAU,EACV,SAAS,EACT,YAAY,EACZ,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,eAAe,GAChB,EAAE,EAAE;IACH,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAChC,MAAM,UAAU,GAAG,eAAe,GAAG,KAAK,CAAC;IAC3C,MAAM,WAAW,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAExD,8CAA8C;IAC9C,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,UAAU,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC,SAAS,CAAC;QAC9C,IAAI,UAAU,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC,YAAY,CAAC;QACjD,OAAO,MAAM,CAAC,WAAW,CAAC;IAC5B,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,GAAG,EAAE;QAC/B,IAAI,UAAU,GAAG,GAAG;YAAE,OAAO,IAAI,CAAC;QAClC,IAAI,UAAU,GAAG,GAAG;YAAE,OAAO,IAAI,CAAC;QAClC,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,cAAc,EAAC,eAAe,EAAC,KAAK,EAAC,MAAM,aAC5D,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,GAAG,cACF,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,aAC1B,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,EACxC,UAAU,IAAI,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,mBAAK,UAAU,UAAU,IAC3D,GACH,EACL,SAAS,IAAI,CACZ,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,YAC1B,YAAY,IAAI,qBAAqB,GACjC,GACH,CACP,IACG,EAGN,KAAC,GAAG,IACF,QAAQ,EAAE,CAAC,EACX,UAAU,EAAC,QAAQ,EACnB,cAAc,EAAC,QAAQ,EACvB,OAAO,EAAC,MAAM,YAEb,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,CAC/D,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,YAChB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,GAC/C,CACR,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,CAC3C,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,+BACf,GAAG,EAClB,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,kBAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,IAC5D,CACR,CAAC,CAAC,CAAC,CACF,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,4BAChB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,4BAAoB,IAClD,CACR,GACG,EAGN,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,EAAC,aAAa,EAAC,QAAQ,aAC7C,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,aACtB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YAC3B,KAAK,GACD,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,eAAe,EAAE,aAC3B,GAAG,OAAG,WAAW,oBAAgB,mBAAmB,EAAE,SAClD,IACH,EACN,MAAC,GAAG,IAAC,UAAU,EAAC,QAAQ,aACrB,SAAS,IAAI,CACZ,MAAC,IAAI,eACH,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,mBAAW,EACnC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,uBAAU,EACvC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,wBAAW,EACzC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,uBAAU,EACvC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,mBAAW,EACzC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,wBAAW,IACnC,CACR,EACA,CAAC,gBAAgB,IAAI,UAAU,GAAG,CAAC,IAAI,CACtC,MAAC,GAAG,eACF,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,mBAAW,EACnC,KAAC,qBAAqB,IAAC,UAAU,EAAE,UAAU,GAAI,IAC7C,CACP,EACA,eAAe,IAAI,KAAC,kBAAkB,KAAG,IACtC,IACF,IACF,CACP,CAAC;AACJ,CAAC,CAAC"}