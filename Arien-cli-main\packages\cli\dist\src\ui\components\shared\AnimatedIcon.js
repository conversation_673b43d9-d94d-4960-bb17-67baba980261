import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect } from 'react';
import { Text } from 'ink';
import { Colors } from '../../colors.js';
export const AnimatedIcon = ({ isPending = false, color = Colors.AccentPurple, animationType = 'default', }) => {
    const [frameIndex, setFrameIndex] = useState(0);
    // Enhanced animation frames with different animation types
    const getAnimationFrames = () => {
        switch (animationType) {
            case 'pulse':
                return ['◆', '◇', '◈', '◇'];
            case 'spin':
                return ['◐', '◓', '◑', '◒'];
            case 'bounce':
                return ['◆', '◇', '◈', '◉', '◈', '◇'];
            default:
                return ['◆', '◇', '◈', '◉'];
        }
    };
    const animatedFrames = getAnimationFrames();
    const staticIcon = '◆';
    useEffect(() => {
        if (!isPending) {
            setFrameIndex(0);
            return;
        }
        // Enhanced timing for smoother animations
        const getAnimationSpeed = () => {
            switch (animationType) {
                case 'pulse':
                    return 200;
                case 'spin':
                    return 100;
                case 'bounce':
                    return 120;
                default:
                    return 150;
            }
        };
        const interval = setInterval(() => {
            setFrameIndex((prev) => (prev + 1) % animatedFrames.length);
        }, getAnimationSpeed());
        return () => clearInterval(interval);
    }, [isPending, animatedFrames.length, animationType]);
    return (_jsx(Text, { color: color, bold: true, children: isPending ? animatedFrames[frameIndex] : staticIcon }));
};
//# sourceMappingURL=AnimatedIcon.js.map